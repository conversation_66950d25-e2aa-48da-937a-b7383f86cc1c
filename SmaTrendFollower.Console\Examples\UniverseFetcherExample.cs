using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Example demonstrating the UniverseFetcher + DynamicUniverse integration
/// </summary>
public static class UniverseFetcherExample
{
    public static async Task RunAsync()
    {
        System.Console.WriteLine("🚀 UniverseFetcher + DynamicUniverse Integration Example");
        System.Console.WriteLine("========================================================");

        // Build configuration
        var configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .Build();

        // Build service provider
        var services = new ServiceCollection()
            .AddSingleton<IConfiguration>(configuration)
            .AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information))
            .AddFullTradingSystem()
            .BuildServiceProvider();

        try
        {
            // Test UniverseFetcherService
            System.Console.WriteLine("\n📊 Testing UniverseFetcherService...");
            var universeFetcher = services.GetRequiredService<IUniverseFetcherService>();
            
            // Check if we have cached symbols
            var cachedSymbols = await universeFetcher.GetCachedSymbolsAsync();
            if (cachedSymbols != null && cachedSymbols.Count > 0)
            {
                System.Console.WriteLine($"✅ Found {cachedSymbols.Count} cached symbols");
                System.Console.WriteLine($"   Sample symbols: {string.Join(", ", cachedSymbols.Take(10))}");
            }
            else
            {
                System.Console.WriteLine("⚠️  No cached symbols found. Fetching from Polygon API...");

                // This would normally be done by the scheduled job
                var fetchedSymbols = await universeFetcher.FetchAllSymbolsAsync();
                System.Console.WriteLine($"✅ Fetched {fetchedSymbols.Count} symbols from Polygon API");
                System.Console.WriteLine($"   Sample symbols: {string.Join(", ", fetchedSymbols.Take(10))}");
            }

            // Test DynamicUniverseProvider integration
            Console.WriteLine("\n🎯 Testing DynamicUniverseProvider integration...");
            var dynamicUniverse = services.GetRequiredService<IDynamicUniverseProvider>();
            
            // Build universe using cached candidates
            var universe = await dynamicUniverse.BuildUniverseAsync();
            var universeList = universe.ToList();
            
            Console.WriteLine($"✅ Built universe with {universeList.Count} symbols");
            if (universeList.Count > 0)
            {
                Console.WriteLine($"   Sample universe symbols: {string.Join(", ", universeList.Take(10))}");
            }

            // Test cache validity
            var isCacheValid = await universeFetcher.IsCacheValidAsync();
            Console.WriteLine($"📅 Cache validity: {(isCacheValid ? "Valid" : "Invalid/Expired")}");

            Console.WriteLine("\n✅ Integration test completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Error during integration test: {ex.Message}");
            Console.WriteLine($"   Stack trace: {ex.StackTrace}");
        }
        finally
        {
            await services.DisposeAsync();
        }
    }

    /// <summary>
    /// Test the Quartz scheduling integration (without actually running the scheduler)
    /// </summary>
    public static async Task TestSchedulingIntegrationAsync()
    {
        Console.WriteLine("\n🕐 Testing Quartz Scheduling Integration...");
        Console.WriteLine("==========================================");

        // Build configuration
        var configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .Build();

        // Build host with scheduling services
        var host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                services.AddSingleton<IConfiguration>(configuration);
                services.AddFullTradingSystem(); // This includes scheduling services
            })
            .Build();

        try
        {
            // Test that the UniverseJobs can be created
            var universeFetcher = host.Services.GetRequiredService<IUniverseFetcherService>();
            var dynamicUniverse = host.Services.GetRequiredService<IDynamicUniverseProvider>();
            var logger = host.Services.GetRequiredService<ILogger<SmaTrendFollower.Scheduling.UniverseJobs>>();

            // Create the job manually to test it
            var universeJob = new SmaTrendFollower.Scheduling.UniverseJobs(
                universeFetcher, 
                dynamicUniverse, 
                logger);

            Console.WriteLine("✅ UniverseJobs can be instantiated successfully");
            Console.WriteLine("✅ All required services are properly registered");
            Console.WriteLine("✅ Quartz integration is ready for production");

            Console.WriteLine("\n📋 Scheduling Configuration:");
            Console.WriteLine("   - Job: UniverseJob");
            Console.WriteLine("   - Trigger: Daily at 8:30 AM ET (12:30 PM UTC)");
            Console.WriteLine("   - Cron: 0 30 12 * * ?");
            Console.WriteLine("   - Weekly symbol fetch: Sundays or when cache is invalid");
            Console.WriteLine("   - Daily universe filtering: Every day at scheduled time");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Error during scheduling test: {ex.Message}");
            Console.WriteLine($"   Stack trace: {ex.StackTrace}");
        }
        finally
        {
            await host.StopAsync();
            host.Dispose();
        }
    }
}
