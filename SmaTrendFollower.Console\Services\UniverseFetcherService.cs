using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Models;
using System.Diagnostics;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for universe fetcher service
/// </summary>
public interface IUniverseFetcherService
{
    /// <summary>
    /// Fetch all US equity symbols from Polygon API and cache in Redis
    /// </summary>
    Task<List<string>> FetchAllSymbolsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cached symbols from Redis
    /// </summary>
    Task<List<string>?> GetCachedSymbolsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if cached symbols are still valid (not stale)
    /// </summary>
    Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Service for fetching full US-equity symbol list from Polygon /v3/reference/tickers
/// Caches results in Redis with key 'universe:candidates' for weekly refresh
/// </summary>
public sealed class UniverseFetcherService : IUniverseFetcherService, IDisposable
{
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly IDatabase? _redis;
    private readonly ConnectionMultiplexer? _connectionMultiplexer;
    private readonly ILogger<UniverseFetcherService> _logger;
    private readonly UniverseFetcherConfig _config;

    private const string PolygonUrl = "https://api.polygon.io/v3/reference/tickers";
    private const string RedisKey = "universe:candidates";

    public UniverseFetcherService(
        IPolygonClientFactory polygonFactory,
        IConfiguration configuration,
        ILogger<UniverseFetcherService> logger)
    {
        _polygonFactory = polygonFactory ?? throw new ArgumentNullException(nameof(polygonFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Load configuration
        _config = UniverseFetcherConfig.Default;
        configuration.GetSection("UniverseFetcher").Bind(_config);

        // Initialize Redis connection if configured
        try
        {
            var redisUrl = configuration["REDIS_URL"];
            if (!string.IsNullOrEmpty(redisUrl))
            {
                var redisDatabase = int.Parse(configuration["REDIS_DATABASE"] ?? "0");
                var redisPassword = configuration["REDIS_PASSWORD"];

                var configOptions = ConfigurationOptions.Parse(redisUrl);
                configOptions.AbortOnConnectFail = false;
                if (!string.IsNullOrEmpty(redisPassword))
                {
                    configOptions.Password = redisPassword;
                }

                _connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
                _redis = _connectionMultiplexer.GetDatabase(redisDatabase);
                _logger.LogInformation("Redis connection established for universe candidates caching");
            }
            else
            {
                _logger.LogInformation("Redis not configured - universe candidates caching disabled");
                _connectionMultiplexer = null;
                _redis = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to Redis - universe candidates caching disabled");
            _connectionMultiplexer = null;
            _redis = null;
        }

        _logger.LogInformation("UniverseFetcherService initialized");
    }

    public async Task<List<string>> FetchAllSymbolsAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var symbols = new List<string>();
        var apiCallCount = 0;

        _logger.LogInformation("Starting full symbol list fetch from Polygon API");

        try
        {
            var httpClient = _polygonFactory.CreateClient();
            var rateLimitHelper = _polygonFactory.GetRateLimitHelper();
            string? nextUrl = null;

            do
            {
                cancellationToken.ThrowIfCancellationRequested();

                var response = await rateLimitHelper.ExecuteAsync(async () =>
                {
                    var url = nextUrl ?? BuildInitialUrl();
                    var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                    
                    _logger.LogDebug("Fetching symbols from: {Url}", url);
                    return await httpClient.GetAsync(urlWithApiKey, cancellationToken);
                }, "UniverseFetch");

                apiCallCount++;

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Polygon API returned {StatusCode} for symbol list fetch", response.StatusCode);
                    break;
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var polygonResponse = JsonSerializer.Deserialize<PolygonTickersResponse>(content);

                if (polygonResponse?.Results == null)
                {
                    _logger.LogWarning("No results in Polygon response");
                    break;
                }

                // Filter symbols for US equities on major exchanges
                var filteredSymbols = polygonResponse.Results
                    .Where(ShouldIncludeSymbol)
                    .Select(r => r.Ticker)
                    .ToList();

                symbols.AddRange(filteredSymbols);
                nextUrl = polygonResponse.NextUrl;

                _logger.LogDebug("Fetched {Count} symbols (total: {Total}), next URL: {NextUrl}",
                    filteredSymbols.Count, symbols.Count, nextUrl != null ? "available" : "none");

                // Rate limiting delay
                if (nextUrl != null && _config.DelayBetweenCalls > 0)
                {
                    await Task.Delay(_config.DelayBetweenCalls, cancellationToken);
                }

            } while (nextUrl != null);

            stopwatch.Stop();

            _logger.LogInformation("Fetched {SymbolCount} symbols in {ElapsedMs}ms with {ApiCalls} API calls",
                symbols.Count, stopwatch.ElapsedMilliseconds, apiCallCount);

            // Cache the results if Redis is available
            if (_redis != null && symbols.Count > 0)
            {
                await CacheSymbolsAsync(symbols);
            }

            return symbols;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching symbol list from Polygon API");
            throw;
        }
    }

    public async Task<List<string>?> GetCachedSymbolsAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return null;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var cachedData = await _redis.StringGetAsync(RedisKey);
            if (!cachedData.HasValue)
            {
                return null;
            }

            var symbolsString = cachedData.ToString();
            return symbolsString.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached symbols");
            return null;
        }
    }

    public async Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return false;
        }

        try
        {
            var ttl = await _redis.KeyTimeToLiveAsync(RedisKey);
            return ttl.HasValue && ttl.Value > TimeSpan.Zero;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache validity");
            return false;
        }
    }

    private string BuildInitialUrl()
    {
        return $"v3/reference/tickers?type=CS&market=stocks&active=true&limit={_config.PageSize}";
    }

    private bool ShouldIncludeSymbol(PolygonTickerResult ticker)
    {
        // Include only common stock on major US exchanges
        return ticker.Active &&
               ticker.Type == "CS" &&
               ticker.Market == "stocks" &&
               (ticker.PrimaryExchange == "NASDAQ" || 
                ticker.PrimaryExchange == "NYSE" || 
                ticker.PrimaryExchange == "AMEX") &&
               !string.IsNullOrEmpty(ticker.Ticker) &&
               ticker.Ticker.Length <= 5; // Exclude complex tickers
    }

    private async Task CacheSymbolsAsync(List<string> symbols)
    {
        if (_redis == null || symbols.Count == 0)
        {
            return;
        }

        try
        {
            var symbolsString = string.Join(",", symbols);
            var ttl = TimeSpan.FromDays(_config.CacheTtlDays);
            
            await _redis.StringSetAsync(RedisKey, symbolsString, ttl);
            
            _logger.LogInformation("Cached {SymbolCount} symbols to Redis with TTL {TTL} days", 
                symbols.Count, _config.CacheTtlDays);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache symbols in Redis");
        }
    }

    public void Dispose()
    {
        _connectionMultiplexer?.Dispose();
    }
}

/// <summary>
/// Configuration for universe fetcher service
/// </summary>
public class UniverseFetcherConfig
{
    /// <summary>
    /// Page size for Polygon API requests
    /// </summary>
    public int PageSize { get; set; } = 1000;

    /// <summary>
    /// Delay between API calls (milliseconds)
    /// </summary>
    public int DelayBetweenCalls { get; set; } = 200;

    /// <summary>
    /// Cache TTL in days
    /// </summary>
    public int CacheTtlDays { get; set; } = 8; // 8 days to ensure weekly refresh

    /// <summary>
    /// Default configuration
    /// </summary>
    public static UniverseFetcherConfig Default => new()
    {
        PageSize = 1000,
        DelayBetweenCalls = 200,
        CacheTtlDays = 8
    };
}
